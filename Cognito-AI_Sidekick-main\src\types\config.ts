export interface Persona {
  Scholar: string;
  Executive: string;
  Storyteller: string;
  Skeptic: string;
  Mentor: string;
  Investigator: string;
  Pragmatist: string;
  Enthusiast: string;
  Curator: string;
  Friend: string;
}

export interface Model {
  id: string;
  host?: 'ollama' | string;
  active?: boolean;
  context_length?: number;
  name?: string;
}



export const CHAT_MODE_OPTIONS = [
  { value: "chat", label: "Chat" },
  { value: "page", label: "Page" },
  { value: "web", label: "Web" },
] as const;

export type ChatMode = typeof CHAT_MODE_OPTIONS[number]['value'];

export type ChatStatus =
  | 'idle'
  | 'typing'
  | 'searching'
  | 'reading'
  | 'thinking'
  | 'done';

export interface QueryStats {
  searchUrls: string[];
  totalTokens: number;
  tokensPerSecond: number;
  processingTimeMs: number;
  startTime?: number;
  endTime?: number;
}
export interface Config {
  personas: Record<string, string>;
  persona: string;
  generateTitle?: boolean;
  backgroundImage?: boolean;
  webMode?: 'Google' | string;
  webLimit?: number;
  serpMaxLinksToVisit?: number;
  contextLimit: number;
  ModelSettingsPanel?: Record<string, unknown>;
  temperature: number;
  maxTokens: number;
  topP: number;
  presencepenalty: number;
  ollamaUrl?: string;
  ollamaConnected?: boolean;
  ollamaError?: string | unknown;
  visibleApiKeys?: boolean;
  fontSize?: number;
  models?: Model[];
  selectedModel?: string;

  chatMode?: Exclude<ChatMode, 'chat'>;
  computeLevel: 'low' | 'medium' | 'high' | string;
  panelOpen: boolean;

  userName?: string;
  userProfile?: string;
  theme?: 'light' | 'dark';
}

export interface ConfigContextType {
  config: Config;
  updateConfig: (newConfig: Partial<Config>) => void;
}
import { useEffect, useState, useRef, useCallback } from 'react';
import localforage from 'localforage';
import { TooltipProvider } from '@/components/ui/tooltip';
import { cn } from '@/src/background/util';

import { useChatTitle } from './hooks/useChatTitle';
import useSendMessage from './hooks/useSendMessage';
import { useUpdateModels } from './hooks/useUpdateModels';
import { Background } from './Background';
import { ChatHistory, ChatMessage, MessageTurn } from './ChatHistory';
import { PrivacyPolicy } from './PrivacyPolicy';
import { About } from './About';
import { useConfig } from './ConfigContext';
import type { ChatStatus } from '../types/config';

import { Header } from './Header';
import { Input } from './Input';
import { Messages } from './Messages';
import {
  downloadImage,
  downloadJson,
  downloadText,
  downloadMarkdown,
} from '../background/messageUtils';
import { Settings } from './Settings';
import storage from '../background/storageUtil';

function bridge() {
  let title = '';
  let textContent = '';
  let htmlContent = '';
  let altTexts = '';
  let tableData = '';
  let metaDescription = '';
  let metaKeywords = '';

  try {
    title = document.title || '';

    const MAX_BODY_CHARS_FOR_DIRECT_EXTRACTION = 5_000_000;
    let bodyElement = document.body;

    if (document.body && document.body.innerHTML.length > MAX_BODY_CHARS_FOR_DIRECT_EXTRACTION) {
      console.warn(
        `[ChromePanion Bridge] Document body is very large (${document.body.innerHTML.length} chars). Attempting to use a cloned, simplified version for text extraction to improve performance/stability.`,
      );

      const clonedBody = document.body.cloneNode(true) as HTMLElement;
      clonedBody
        .querySelectorAll('script, style, noscript, iframe, embed, object')
        .forEach((el) => el.remove());
      textContent = (clonedBody.textContent || '').replace(/\s\s+/g, ' ').trim();
      htmlContent = document.body.innerHTML.replace(/\s\s+/g, ' ');
    } else if (document.body) {
      textContent = (document.body.innerText || '').replace(/\s\s+/g, ' ').trim();
      htmlContent = (document.body.innerHTML || '').replace(/\s\s+/g, ' ');
    } else {
      console.warn('[ChromePanion Bridge] document.body is not available.');
    }

    altTexts = Array.from(document.images)
      .map((img) => img.alt)
      .filter((alt) => alt && alt.trim().length > 0)
      .join('. ');

    tableData = Array.from(document.querySelectorAll('table'))
      .map((table) => (table.innerText || '').replace(/\s\s+/g, ' '))
      .join('\n');

    const descElement = document.querySelector('meta[name="description"]');
    metaDescription = descElement ? descElement.getAttribute('content') || '' : '';

    const keywordsElement = document.querySelector('meta[name="keywords"]');
    metaKeywords = keywordsElement ? keywordsElement.getAttribute('content') || '' : '';
  } catch (error) {
    console.error('[ChromePanion Bridge] Error during content extraction:', error);
    let errorMessage = 'Unknown extraction error';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    }
    return JSON.stringify({
      error: `Extraction failed: ${errorMessage}`,
      title: document.title || 'Error extracting title',
      text: '',
      html: '',
      altTexts: '',
      tableData: '',
      meta: { description: '', keywords: '' },
    });
  }

  const MAX_OUTPUT_STRING_LENGTH = 10_000_000;

  let responseCandidate = {
    title,
    text: textContent,
    html: htmlContent,
    altTexts,
    tableData,
    meta: {
      description: metaDescription,
      keywords: metaKeywords,
    },
  };

  if (JSON.stringify(responseCandidate).length > MAX_OUTPUT_STRING_LENGTH) {
    console.warn(
      '[ChromePanion Bridge] Total extracted content is very large. Attempting to truncate.',
    );
    const availableLength =
      MAX_OUTPUT_STRING_LENGTH -
      JSON.stringify({ ...responseCandidate, text: '', html: '' }).length;
    let remainingLength = availableLength;

    if (responseCandidate.text.length > remainingLength * 0.6) {
      responseCandidate.text =
        responseCandidate.text.substring(0, Math.floor(remainingLength * 0.6)) + '... (truncated)';
    }
    remainingLength = availableLength - responseCandidate.text.length;

    if (responseCandidate.html.length > remainingLength * 0.8) {
      responseCandidate.html =
        responseCandidate.html.substring(0, Math.floor(remainingLength * 0.8)) + '... (truncated)';
    }
    console.warn(
      '[ChromePanion Bridge] Content truncated. Final approx length:',
      JSON.stringify(responseCandidate).length,
    );
  }

  return JSON.stringify(responseCandidate);
}

async function injectBridge() {
  const queryOptions = { active: true, lastFocusedWindow: true };
  const [tab] = await chrome.tabs.query(queryOptions);

  if (
    !tab?.id ||
    tab.url?.startsWith('chrome://') ||
    tab.url?.startsWith('chrome-extension://') ||
    tab.url?.startsWith('about:')
  ) {
    // Added about:
    storage.deleteItem('pagestring');
    storage.deleteItem('pagehtml');
    storage.deleteItem('alttexts');
    storage.deleteItem('tabledata');
    return;
  }

  storage.deleteItem('pagestring');
  storage.deleteItem('pagehtml');
  storage.deleteItem('alttexts');
  storage.deleteItem('tabledata');

  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId: tab.id },
      func: bridge,
    });

    if (
      !results ||
      !Array.isArray(results) ||
      results.length === 0 ||
      !results[0] ||
      typeof results[0].result !== 'string'
    ) {
      console.error(
        '[ChromePanion:] Bridge function execution returned invalid or unexpected results structure:',
        results,
      );
      return;
    }

    const rawResult = results[0].result;
    let res: any;
    try {
      res = JSON.parse(rawResult);
    } catch (parseError) {
      console.error(
        '[ChromePanion:] Failed to parse JSON result from bridge:',
        parseError,
        'Raw result string:',
        rawResult,
      );
      return;
    }

    if (res.error) {
      console.error(
        '[ChromePanion:] Bridge function reported an error:',
        res.error,
        'Title:',
        res.title,
      );
      return;
    }

    try {
      storage.setItem('pagestring', res?.text ?? '');
      storage.setItem('pagehtml', res?.html ?? '');
      storage.setItem('alttexts', res?.altTexts ?? '');
      storage.setItem('tabledata', res?.tableData ?? '');
    } catch (storageError) {
      console.error('[ChromePanion:] Storage error after successful extraction:', storageError);
      storage.deleteItem('pagestring');
      storage.deleteItem('pagehtml');
      storage.deleteItem('alttexts');
      storage.deleteItem('tabledata');
    }
  } catch (execError) {
    console.error('[ChromePanion:] Bridge function execution failed:', execError);
    if (
      execError instanceof Error &&
      (execError.message.includes('Cannot access contents of url "chrome://') ||
        execError.message.includes('Cannot access a chrome extension URL') ||
        execError.message.includes('Cannot access contents of url "about:'))
    ) {
      console.warn('[ChromePanion:] Cannot access restricted URL.');
    }
  }
}

const generateChatId = () => `chat_${Math.random().toString(16).slice(2)}`;



const ChromePanion = () => {
  const [turns, setTurns] = useState<MessageTurn[]>([]);
  const [message, setMessage] = useState('');
  const [chatId, setChatId] = useState(generateChatId());
  const [webContent, setWebContent] = useState('');
  const [pageContent, setPageContent] = useState('');
  const [isLoading, setLoading] = useState(false);
  const [settingsMode, setSettingsMode] = useState(false);
  const [historyMode, setHistoryMode] = useState(false);
  const [privacyMode, setPrivacyMode] = useState(false);
  const [aboutMode, setAboutMode] = useState(false);
  const { config, updateConfig } = useConfig();

  const [currentTabInfo, setCurrentTabInfo] = useState<{ id: number | null; url: string }>({
    id: null,
    url: '',
  });

  const containerRef = useRef<HTMLDivElement>(null);
  const lastInjectedRef = useRef<{ id: number | null; url: string }>({ id: null, url: '' });


  const [chatStatus, setChatStatus] = useState<ChatStatus>('idle');

  useEffect(() => {
    const resizeObserver = new ResizeObserver(() => {
      if (containerRef.current) {
        containerRef.current.style.minHeight = '100dvh';
        requestAnimationFrame(() => {
          if (containerRef.current) {
            containerRef.current.style.minHeight = '';
          }
        });
      }
    });
    if (containerRef.current) resizeObserver.observe(containerRef.current);
    return () => resizeObserver.disconnect();
  }, []);

  useEffect(() => {
    if (config?.chatMode !== 'page') return;

    const checkAndInject = async () => {
      const [tab] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
      if (!tab?.id || !tab.url) return;

      if (
        tab.url.startsWith('chrome://') ||
        tab.url.startsWith('chrome-extension://') ||
        tab.url.startsWith('about:')
      ) {
        if (lastInjectedRef.current.id !== tab.id || lastInjectedRef.current.url !== tab.url) {
          storage.deleteItem('pagestring');
          storage.deleteItem('pagehtml');
          storage.deleteItem('alttexts');
          storage.deleteItem('tabledata');
        }
        lastInjectedRef.current = { id: tab.id, url: tab.url };
        setCurrentTabInfo({ id: tab.id, url: tab.url });
        return;
      }

      if (tab.id !== lastInjectedRef.current.id || tab.url !== lastInjectedRef.current.url) {
        lastInjectedRef.current = { id: tab.id, url: tab.url };
        setCurrentTabInfo({ id: tab.id, url: tab.url });
        await injectBridge();
      } else {
      }
    };

    checkAndInject();

    const handleTabActivated = (activeInfo: chrome.tabs.TabActiveInfo) => {
      chrome.tabs.get(activeInfo.tabId, (tab) => {
        if (chrome.runtime.lastError) {
          console.warn(
            `[ChromePanion ] Error getting tab info on activation: ${chrome.runtime.lastError.message}`,
          );
          return;
        }
        checkAndInject();
      });
    };

    const handleTabUpdated = (
      tabId: number,
      changeInfo: chrome.tabs.TabChangeInfo,
      tab: chrome.tabs.Tab,
    ) => {
      if (
        tab.active &&
        (changeInfo.status === 'complete' || (changeInfo.url && tab.status === 'complete'))
      ) {
        checkAndInject();
      }
    };

    chrome.tabs.onActivated.addListener(handleTabActivated);
    chrome.tabs.onUpdated.addListener(handleTabUpdated);

    return () => {
      chrome.tabs.onActivated.removeListener(handleTabActivated);
      chrome.tabs.onUpdated.removeListener(handleTabUpdated);
      lastInjectedRef.current = { id: null, url: '' };
    };
  }, [config?.chatMode]);

  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (settingsMode || historyMode) {
        return;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [config?.chatMode, updateConfig, settingsMode, historyMode]);

  const { chatTitle, setChatTitle } = useChatTitle(isLoading, turns, message);
  const { onSend, onStop } = useSendMessage(
    isLoading,
    message,
    turns,
    webContent,
    config,
    setTurns,
    setMessage,
    setWebContent,
    setPageContent,
    setLoading,
    setChatStatus,
  );

  const handleSendMessage = useCallback((messageText: string) => {
    setMessage(messageText);
    onSend(messageText);
  }, [onSend, setMessage]);
  useUpdateModels();

  const reset = () => {
    // Cancel any ongoing chat operations first
    if (isLoading) {
      onStop();
    }

    setTurns([]);
    setPageContent('');
    setWebContent('');
    setLoading(false);
    updateConfig({ chatMode: 'web', computeLevel: 'low' });
    setChatStatus('idle');
    setMessage('');
    setChatTitle('');
    setChatId(generateChatId());
    setHistoryMode(false);
    setSettingsMode(false);
    setPrivacyMode(false);

    if (containerRef.current) {
      containerRef.current.scrollTop = 0;
    }
  };

  const onReload = () => {
    // Cancel any ongoing chat operations first
    if (isLoading) {
      onStop();
    }

    setTurns((prevTurns) => {
      if (prevTurns.length < 2) return prevTurns;
      const last = prevTurns[prevTurns.length - 1];
      const secondLast = prevTurns[prevTurns.length - 2];
      if (last.role === 'assistant' && secondLast.role === 'user') {
        setMessage(secondLast.rawContent);
        return prevTurns.slice(0, -2);
      }
      return prevTurns;
    });
    setLoading(false);
    setChatStatus('idle');
  };

  const loadChat = (chat: ChatMessage) => {
    // Cancel any ongoing chat operations first
    if (isLoading) {
      onStop();
    }

    setChatTitle(chat.title || '');
    setTurns(chat.turns);
    setChatId(chat.id);
    setHistoryMode(false);
    setChatStatus('idle');
    setSettingsMode(false);
    setPrivacyMode(false);

    if (chat.chatMode !== 'page') {
      storage.deleteItem('pagestring');
      storage.deleteItem('pagehtml');
      storage.deleteItem('alttexts');
      storage.deleteItem('tabledata');
      lastInjectedRef.current = { id: null, url: '' };
    }
  };

  const deleteAll = async () => {
    try {
      // Cancel any ongoing chat operations first
      if (isLoading) {
        onStop();
      }

      const keys = await localforage.keys();
      const chatKeys = keys.filter((key) => key.startsWith('chat_'));
      if (chatKeys.length === 0 && turns.length === 0) return;
      await Promise.all(chatKeys.map((key) => localforage.removeItem(key)));

      reset();
    } catch (error) {
      console.error('[ChromePanion] Error deleting all chats:', error);
    }
  };

  useEffect(() => {
    if (turns.length > 0 && !historyMode && !settingsMode && !privacyMode) {
      const savedChat: ChatMessage = {
        id: chatId,
        title: chatTitle || `Chat ${new Date(Date.now()).toLocaleString()}`,
        turns,
        last_updated: Date.now(),
        model: config?.selectedModel,
        chatMode: config?.chatMode,
        webMode: config?.chatMode === 'web' ? config.webMode : undefined,
      };
      localforage.setItem(chatId, savedChat).catch((err) => {
        console.error(`[ChromePanion ] Error saving chat ${chatId}:`, err);
      });
    }
  }, [
    chatId,
    turns,
    chatTitle,
    config?.selectedModel,
    config?.chatMode,
    config?.webMode,
    historyMode,
    settingsMode,
    privacyMode,
  ]);

  useEffect(() => {
    if (chatStatus === 'done' || chatStatus === 'idle') {
      const timer = setTimeout(() => {
        setChatStatus('idle');
      }, 1500);
      return () => clearTimeout(timer);
    }
  }, [chatStatus]);

  useEffect(() => {
    let cancelled = false;

    const handlePanelOpen = async () => {
      if (cancelled) return;
      reset();

      try {
        const [tab] = await chrome.tabs.query({ active: true, lastFocusedWindow: true });
        if (!cancelled && tab?.id && tab.url) {
          setCurrentTabInfo({ id: tab.id, url: tab.url });

          if (
            tab.url.startsWith('chrome://') ||
            tab.url.startsWith('chrome-extension://') ||
            tab.url.startsWith('about:')
          ) {
            storage.deleteItem('pagestring');
            storage.deleteItem('pagehtml');
            storage.deleteItem('alttexts');
            storage.deleteItem('tabledata');
            lastInjectedRef.current = { id: null, url: '' };
          } else {
          }
        } else if (!cancelled) {
          lastInjectedRef.current = { id: null, url: '' };
          setCurrentTabInfo({ id: null, url: '' });
          storage.deleteItem('pagestring');
          storage.deleteItem('pagehtml');
          storage.deleteItem('alttexts');
          storage.deleteItem('tabledata');
        }
      } catch (error) {
        if (!cancelled) {
          console.error('[ChromePanion - Revised] Error during panel open tab check:', error);
          lastInjectedRef.current = { id: null, url: '' };
          setCurrentTabInfo({ id: null, url: '' });
          storage.deleteItem('pagestring');
          storage.deleteItem('pagehtml');
          storage.deleteItem('alttexts');
          storage.deleteItem('tabledata');
        }
      }
    };

    handlePanelOpen();

    return () => {
      cancelled = true;
      // Cancel any ongoing chat operations when component unmounts
      if (isLoading) {
        onStop();
      }
      storage.deleteItem('pagestring');
      storage.deleteItem('pagehtml');
      storage.deleteItem('alttexts');
      storage.deleteItem('tabledata');
      reset();
      lastInjectedRef.current = { id: null, url: '' };
    };
  }, []);

  // Handle extension close/unload events to cancel ongoing operations
  useEffect(() => {
    const handleBeforeUnload = () => {
      if (isLoading) {
        onStop();
      }
    };

    const handleUnload = () => {
      if (isLoading) {
        onStop();
      }
    };

    // Add event listeners for window close/unload
    window.addEventListener('beforeunload', handleBeforeUnload);
    window.addEventListener('unload', handleUnload);

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('unload', handleUnload);
    };
  }, [isLoading, onStop]);

  const handleEditTurn = (index: number, newContent: string) => {
    setTurns((prevTurns) => {
      const updatedTurns = [...prevTurns];
      if (updatedTurns[index]) {
        updatedTurns[index] = { ...updatedTurns[index], rawContent: newContent };
      }
      return updatedTurns;
    });
  };

  return (
    <TooltipProvider delayDuration={300}>
      <div
        ref={containerRef}
        className={cn('w-full h-dvh p-0 overflow-hidden', 'flex flex-col bg-[var(--bg)]')}
      >
        <Header
          deleteAll={deleteAll}
          downloadImage={() => downloadImage(turns)}
          downloadJson={() => downloadJson(turns)}
          downloadText={() => downloadText(turns)}
          downloadMarkdown={() => downloadMarkdown(turns)}
          historyMode={historyMode}
          privacyMode={privacyMode}
          aboutMode={aboutMode}
          reset={reset}
          setHistoryMode={setHistoryMode}
          setSettingsMode={setSettingsMode}
          setPrivacyMode={setPrivacyMode}
          setAboutMode={setAboutMode}
          settingsMode={settingsMode}
        />
        <div className='flex flex-col flex-1 min-h-0 no-scrollbar overflow-y-auto relative'>
          {settingsMode && <Settings />}

          {!settingsMode && privacyMode && <PrivacyPolicy />}

          {!settingsMode && !privacyMode && aboutMode && <About />}

          {!settingsMode && !privacyMode && !aboutMode && historyMode && (
            <ChatHistory
              className='flex-1 w-full min-h-0'
              loadChat={loadChat}
              onDeleteAll={deleteAll}
            />
          )}

          {!settingsMode && !privacyMode && !aboutMode && !historyMode && (
            <div className='flex flex-col flex-1 min-h-0 relative'>
              <Messages
                turns={turns}
                settingsMode={settingsMode}
                onReload={onReload}
                onEditTurn={handleEditTurn}
                onSendMessage={handleSendMessage}
              />



            </div>
          )}
        </div>
        {!settingsMode && !privacyMode && !aboutMode && !historyMode && (
          <div className='p-2 relative z-[10]'>
            <Input
              isLoading={isLoading}
              message={message}
              setMessage={setMessage}
              onSend={() => onSend(message)}
              onStopRequest={onStop}
            />
          </div>
        )}

        {config?.backgroundImage ? <Background /> : null}
      </div>
    </TooltipProvider>
  );
};

export default ChromePanion;
